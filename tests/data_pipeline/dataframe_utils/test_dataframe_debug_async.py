import pytest
import asyncio
import pandas as pd
import os
from pathlib import Path
from datetime import datetime

from dags.data_pipeline.dataframe_utils.dataframe_debug_async import (
    AsyncDataframeDebugger,
    SaveFormat,
    Priority
)

@pytest.mark.asyncio
async def test_save_dataframe_multiple_formats_async(tmp_path):
    df = pd.DataFrame({'a': [1, 2], 'b': ['x', 'y']})
    formats = [
        (SaveFormat.CSV, "test.csv"),
        (SaveFormat.EXCEL, "test.xlsx"),
        (SaveFormat.PARQUET, "test.parquet"),
        (SaveFormat.FEATHER, "test.feather"),
        (SaveFormat.JSON, "test.json"),
    ]
    async with AsyncDataframeDebugger(path=tmp_path, max_workers=2) as debugger:
        task_ids = []
        for fmt, fname in formats:
            task_id = await debugger.save_dataframe(df, fname, fmt)
            task_ids.append((task_id, fname))
        await debugger.wait_for_completion(timeout=10)
    for _, fname in task_ids:
        file_path = tmp_path / fname
        assert file_path.exists()

@pytest.mark.asyncio
async def test_batch_save_context_manager(tmp_path):
    dfs = [pd.DataFrame({'x': [i, i+1], 'y': [str(i), str(i+1)]}) for i in range(0, 10, 2)]
    filenames = [f"batch_{i}.xlsx" for i in range(len(dfs))]
    async with AsyncDataframeDebugger(path=tmp_path, max_workers=2) as debugger:
        async with debugger.batch_save():
            for df, fname in zip(dfs, filenames):
                await debugger.save_dataframe(df, fname, file_format=SaveFormat.EXCEL)
        # After context exit, all files should be saved
    for fname in filenames:
        assert (tmp_path / fname).exists()

@pytest.mark.asyncio
async def test_progress_callback_invocation(tmp_path):
    df = pd.DataFrame({'a': [1, 2]})
    progress_events = []
    async def progress_callback(data):
        progress_events.append(data['status'])
    async with AsyncDataframeDebugger(path=tmp_path, max_workers=1) as debugger:
        debugger.add_progress_callback(progress_callback)
        await debugger.save_dataframe(df, "cb_test.xlsx", SaveFormat.EXCEL)
        await debugger.wait_for_completion(timeout=5)
    # Should see at least 'queued', 'processing', 'completed'
    assert 'queued' in progress_events
    assert 'processing' in progress_events
    assert 'completed' in progress_events

@pytest.mark.asyncio
async def test_save_empty_dataframe_raises_valueerror(tmp_path):
    df_empty = pd.DataFrame()
    async with AsyncDataframeDebugger(path=tmp_path) as debugger:
        with pytest.raises(ValueError, match="empty or None DataFrame"):
            await debugger.save_dataframe(df_empty, "empty.xlsx", SaveFormat.EXCEL)
        with pytest.raises(ValueError, match="empty or None DataFrame"):
            await debugger.save_dataframe(None, "none.xlsx", SaveFormat.EXCEL)

@pytest.mark.asyncio
async def test_unsupported_file_format_raises_valueerror(tmp_path):
    df = pd.DataFrame({'a': [1]})
    async with AsyncDataframeDebugger(path=tmp_path) as debugger:
        with pytest.raises(ValueError, match="Unsupported file format"):
            await debugger.save_dataframe(df, "badfile.unsupported", "unsupported")

@pytest.mark.asyncio
async def test_queue_full_raises_asyncio_queuefull(tmp_path):
    df = pd.DataFrame({'a': [1]})
    max_queue_size = 2
    async with AsyncDataframeDebugger(path=tmp_path, max_queue_size=max_queue_size, max_workers=1) as debugger:
        # Fill the queue
        for i in range(max_queue_size):
            await debugger.save_dataframe(df, f"file_{i}.xlsx", SaveFormat.EXCEL)
        # Next put should raise
        with pytest.raises(asyncio.QueueFull):
            await asyncio.wait_for(
                debugger.save_dataframe(df, "overflow.xlsx", SaveFormat.EXCEL),
                timeout=2
            )