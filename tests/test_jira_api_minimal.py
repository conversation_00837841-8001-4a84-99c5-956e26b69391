import pytest
from unittest.mock import MagicMock, AsyncMock

@pytest.fixture
def mock_db_manager():
    # Mock for sync session
    mock_session = MagicMock()
    mock_context_manager = MagicMock()
    mock_context_manager.__enter__.return_value = mock_session
    mock_context_manager.__exit__.return_value = None

    # Mock for async session
    mock_async_session = AsyncMock()
    mock_async_context_manager = AsyncMock()
    mock_async_context_manager.__aenter__.return_value = mock_async_session
    mock_async_context_manager.__aexit__.return_value = None

    # The database manager mock
    db_manager = MagicMock()
    db_manager.session.return_value = mock_context_manager
    db_manager.async_session.return_value = mock_async_context_manager

    # If you need to mock update_schema().async_session() chain:
    mock_schema_manager = MagicMock()
    mock_schema_manager.async_session.return_value = mock_async_context_manager
    db_manager.update_schema.return_value = mock_schema_manager

    return db_manager, mock_session, mock_async_session

def test_sync_context_manager(mock_db_manager):
    db_manager, mock_session, _ = mock_db_manager
    with db_manager.session() as session:
        assert session is mock_session

@pytest.mark.asyncio
async def test_async_context_manager(mock_db_manager):
    db_manager, _, mock_async_session = mock_db_manager
    async with db_manager.async_session() as session:
        assert session is mock_async_session

@pytest.mark.asyncio
async def test_chained_async_context_manager(mock_db_manager):
    db_manager, _, mock_async_session = mock_db_manager
    # This simulates: db_manager.update_schema('foo').async_session()
    async with db_manager.update_schema('foo').async_session() as session:
        assert session is mock_async_session