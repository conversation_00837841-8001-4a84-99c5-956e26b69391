# Container Tests

This directory contains comprehensive tests for the `containers.py` module with allure reporting and coverage analysis.

## Overview

The test suite covers:

- **DatabaseSessionManagerContainer**: Dependency injection for database sessions
- **ManagedPostgresSessionManager**: Managed database session lifecycle
- **ApplicationContainer**: Application-wide dependency injection
- **PostgresSessionManager**: Core database session management
- **Utility Functions**: Helper functions and decorators
- **Error Handling**: Exception scenarios and edge cases

## Test Structure

```
tests/container/
├── test_containers_comprehensive.py  # Main test suite
├── pytest.ini                       # Pytest configuration
├── requirements-test.txt             # Test dependencies
├── run_tests.py                     # Test runner script
└── README.md                        # This file
```

## Quick Start

### 1. Setup with UV (Recommended)

If you're using `uv` for package management:

```bash
# Setup environment with legacy drivers (psycopg2)
python setup_uv_env.py

# Or manually with uv
uv sync --group test --group legacy

# Run tests with uv
uv run pytest test_basic_setup.py
uv run python run_tests.py --coverage --allure
```

### 2. Setup with Pip (Alternative)

```bash
# Check if environment is properly configured
python check_environment.py

# Check database driver compatibility
python run_tests.py --check-drivers

# Install test dependencies
python run_tests.py --install-deps

# Or manually
pip install -r requirements-test.txt
```

### 2. Run Tests

```bash
# Run all tests with coverage and allure reporting
python run_tests.py --coverage --allure

# Run only unit tests
python run_tests.py --unit --coverage

# Run tests in parallel
python run_tests.py --parallel --verbose

# Clean old reports and run tests
python run_tests.py --clean --coverage --allure
```

### 3. View Reports

After running tests with reporting options:

- **Coverage Report**: Open `htmlcov/index.html` in your browser
- **Allure Report**: Open `allure-report/index.html` in your browser

## Test Categories

### Unit Tests
Tests individual components in isolation with mocked dependencies.

```bash
python run_tests.py --unit
```

### Integration Tests
Tests component interactions and real database connections.

```bash
python run_tests.py --integration
```

### Async Tests
Tests asynchronous functionality and lifecycle management.

```bash
pytest -m async
```

## Test Features

### Allure Reporting
- **Feature-based organization**: Tests grouped by functionality
- **Severity levels**: Critical, Normal, Minor classifications
- **Step-by-step execution**: Detailed test execution flow
- **Rich attachments**: Screenshots, logs, and data

### Coverage Analysis
- **Line coverage**: Tracks executed code lines
- **Branch coverage**: Tracks conditional branches
- **Function coverage**: Tracks function calls
- **Missing lines**: Identifies untested code

### Mocking Strategy
- **Database engines**: Mocked SQLAlchemy engines
- **KeePass manager**: Mocked credential access
- **Async operations**: Mocked async context managers
- **External dependencies**: Isolated from real services

## Test Configuration

### pytest.ini
```ini
[tool:pytest]
testpaths = .
python_files = test_*.py
addopts = --strict-markers --cov=dags.data_pipeline.containers
asyncio_mode = auto
```

### Markers
- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.slow`: Slow-running tests
- `@pytest.mark.database`: Database-dependent tests
- `@pytest.mark.async`: Asynchronous tests

### Allure Decorators
- `@allure.feature()`: Test feature grouping
- `@allure.story()`: Test story grouping
- `@allure.severity()`: Test importance level
- `@allure.title()`: Custom test titles

## Running Specific Tests

### By Test Class
```bash
pytest test_containers_comprehensive.py::TestPostgresSessionManager
```

### By Test Method
```bash
pytest test_containers_comprehensive.py::TestPostgresSessionManager::test_postgres_session_manager_init
```

### By Marker
```bash
pytest -m "unit and not slow"
pytest -m "integration or database"
```

### With Custom Options
```bash
# Verbose output with coverage
pytest -v --cov=dags.data_pipeline.containers --cov-report=html

# Parallel execution
pytest -n auto

# Stop on first failure
pytest -x

# Run last failed tests
pytest --lf
```

## Coverage Requirements

The test suite maintains a minimum coverage threshold of **80%**.

### Coverage Reports
- **Terminal**: Shows missing lines during test run
- **HTML**: Detailed interactive coverage report
- **XML**: Machine-readable coverage data

### Coverage Exclusions
```python
# pragma: no cover - Exclude from coverage
def debug_function():  # pragma: no cover
    pass
```

## Continuous Integration

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    python tests/container/run_tests.py --coverage --allure
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage.xml
    
- name: Upload Allure Results
  uses: actions/upload-artifact@v3
  with:
    name: allure-results
    path: allure-results/
```

## Troubleshooting

### Common Issues

1. **psycopg Driver Conflicts**
   ```bash
   # Error: couldn't import psycopg 'c' implementation
   # This happens when psycopg3 is installed alongside psycopg2

   # Check for conflicts
   python check_environment.py

   # Fix: Uninstall psycopg3 and ensure psycopg2 is installed
   pip uninstall psycopg
   pip install psycopg2-binary

   # Verify drivers
   python run_tests.py --check-drivers
   ```

2. **Import Errors**
   ```bash
   # Ensure PYTHONPATH includes project root
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"
   ```

3. **Database Connection Errors**
   ```bash
   # Use mocked tests for CI/CD
   pytest -m "not database"
   ```

4. **Async Test Issues**
   ```bash
   # Ensure pytest-asyncio is installed
   pip install pytest-asyncio
   ```

5. **Missing Database Drivers**
   ```bash
   # Install required drivers
   pip install psycopg2-binary asyncpg

   # Check installation
   python -c "import psycopg2; print(psycopg2.__version__)"
   python -c "import asyncpg; print(asyncpg.__version__)"
   ```

6. **Allure Command Not Found**
   ```bash
   # Install Allure CLI
   npm install -g allure-commandline
   # Or use Docker
   docker run -p 4040:4040 -v ${PWD}/allure-results:/app/allure-results frankescobar/allure-docker-service
   ```

### Debug Mode
```bash
# Run with dataframe_utils output
pytest --pdb --pdbcls=IPython.terminal.debugger:Pdb

# Capture output
pytest -s --capture=no
```

## Best Practices

### Test Organization
- Group related tests in classes
- Use descriptive test names
- Add comprehensive docstrings
- Use appropriate markers

### Mocking
- Mock external dependencies
- Use realistic test data
- Verify mock interactions
- Clean up after tests

### Assertions
- Use specific assertions
- Include helpful error messages
- Test both positive and negative cases
- Verify side effects

### Performance
- Use fixtures for expensive setup
- Run tests in parallel when possible
- Mark slow tests appropriately
- Profile test execution time

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Add appropriate allure decorators
3. Include both positive and negative test cases
4. Update this README if needed
5. Ensure coverage requirements are met

## Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [Allure Framework](https://docs.qameta.io/allure/)
- [Coverage.py](https://coverage.readthedocs.io/)
- [SQLAlchemy Testing](https://docs.sqlalchemy.org/en/14/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites)
