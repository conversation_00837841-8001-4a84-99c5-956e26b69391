import pytest
import os
from pathlib import Path

@pytest.fixture
def context():
    return {}

# Configure allure results directory
@pytest.fixture(scope="session", autouse=True)
def allure_results_dir():
    """Ensure allure results directory exists"""
    results_dir = Path("allure_results")
    results_dir.mkdir(exist_ok=True)
    return results_dir

# Configure allure environment
@pytest.fixture(scope="session", autouse=True)
def allure_environment():
    """Set up allure environment variables"""
    os.environ.setdefault("ALLURE_RESULTS_DIR", "allure_results")
    return os.environ.get("ALLURE_RESULTS_DIR")

# Handle spec coverage plugin configuration
def pytest_configure(config):
    """Configure pytest plugins and handle spec coverage"""
    # Ensure allure results directory exists
    allure_dir = Path("allure_results")
    allure_dir.mkdir(exist_ok=True)
    
    # Configure spec coverage plugin to handle empty scenarios gracefully
    if hasattr(config, 'pluginmanager'):
        spec_coverage_plugin = config.pluginmanager.get_plugin('pytest_allure_spec_coverage')
        if spec_coverage_plugin:
            # Set a default minimum scenario count to prevent division by zero
            setattr(spec_coverage_plugin, 'min_scenarios', 1)

def pytest_collection_modifyitems(config, items):
    """Modify test items to add allure labels"""
    for item in items:
        # Add allure labels for BDD tests
        if hasattr(item, 'get_closest_marker') and item.get_closest_marker('bdd'):
            item.add_marker(pytest.mark.feature("BDD Tests"))
