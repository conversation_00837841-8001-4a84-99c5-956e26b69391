#!/usr/bin/env python3
"""
Test script for the debugging functionality.
This script simulates various scenarios that might cause the pipeline to get stuck.
"""

import asyncio
import aiohttp
import logging
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Enable debugging
os.environ["ENABLE_DEBUG_MONITOR"] = "true"

from dags.data_pipeline.debug.debug_utils import (
    debug_async_function, debug_http_request, debug_queue_operation,
    debug_timeout_detection, log_debug_summary, enable_debug_monitoring,
    rate_limit_tracker
)

logger = logging.getLogger(__name__)


@debug_async_function("test_http_requests")
async def test_http_requests():
    """Test HTTP request debugging"""
    print("🌐 Testing HTTP request debugging...")
    
    async with aiohttp.ClientSession() as session:
        # Simulate normal request
        async with debug_http_request(session, "GET", "https://httpbin.org/get"):
            async with session.get("https://httpbin.org/get") as response:
                if response.status == 200:
                    print("✅ Normal HTTP request logged")
        
        # Simulate rate limit (this will likely return 200, but we'll log it as 429 for testing)
        rate_limit_tracker.record_rate_limit("https://api.example.com/test", 5000)
        print("✅ Rate limit event recorded")


@debug_async_function("test_queue_operations")
async def test_queue_operations():
    """Test queue operation debugging"""
    print("📦 Testing queue operation debugging...")
    
    test_queue = asyncio.Queue(maxsize=5)
    
    # Test put operations
    for i in range(3):
        async with debug_queue_operation(test_queue, "put", "test_queue"):
            await test_queue.put(f"item_{i}")
    
    print(f"✅ Queue size after puts: {test_queue.qsize()}")
    
    # Test get operations
    for i in range(3):
        async with debug_queue_operation(test_queue, "get", "test_queue"):
            item = await test_queue.get()
            print(f"   Got item: {item}")
        
        # Simulate task_done
        async with debug_queue_operation(test_queue, "task_done", "test_queue"):
            test_queue.task_done()
    
    print("✅ Queue operations logged")


@debug_async_function("test_slow_operation")
async def test_slow_operation():
    """Test detection of slow operations"""
    print("🐌 Testing slow operation detection...")
    
    # This should trigger timeout detection
    async with debug_timeout_detection("slow_test_operation", timeout_seconds=3):
        await asyncio.sleep(5)  # Sleep longer than timeout
    
    print("✅ Slow operation completed")


@debug_async_function("test_stuck_simulation")
async def test_stuck_simulation():
    """Simulate a stuck operation"""
    print("🚨 Testing stuck operation simulation...")
    
    # Create a queue that will appear stuck
    stuck_queue = asyncio.Queue()
    
    async def producer():
        """Producer that stops producing"""
        await stuck_queue.put("item1")
        await asyncio.sleep(2)  # Stop producing
        print("   Producer stopped (simulating stuck state)")
    
    async def consumer():
        """Consumer waiting for items"""
        try:
            async with debug_timeout_detection("stuck_consumer", timeout_seconds=5):
                while True:
                    async with debug_queue_operation(stuck_queue, "get", "stuck_queue"):
                        item = await stuck_queue.get()
                        print(f"   Consumer got: {item}")
                        stuck_queue.task_done()
        except asyncio.TimeoutError:
            print("   Consumer timed out (expected)")
    
    # Run producer and consumer
    try:
        await asyncio.wait_for(
            asyncio.gather(producer(), consumer()),
            timeout=10
        )
    except asyncio.TimeoutError:
        print("✅ Stuck simulation completed (timeout expected)")


async def test_rate_limit_tracking():
    """Test rate limit tracking functionality"""
    print("⏱️  Testing rate limit tracking...")
    
    # Simulate multiple rate limit events
    for i in range(5):
        rate_limit_tracker.record_rate_limit(f"https://api.example.com/endpoint_{i}", 1000 * (i + 1))
        await asyncio.sleep(0.1)
    
    # Get stats
    stats = rate_limit_tracker.get_rate_limit_stats()
    print(f"   Rate limit stats: {stats}")
    print("✅ Rate limit tracking tested")


async def main():
    """Main test function"""
    print("🐛 Testing Debugging Functionality")
    print("=" * 50)
    
    # Enable debugging
    enable_debug_monitoring()
    
    try:
        # Run tests
        await test_http_requests()
        await asyncio.sleep(1)
        
        await test_queue_operations()
        await asyncio.sleep(1)
        
        await test_rate_limit_tracking()
        await asyncio.sleep(1)
        
        # Test dataframe_utils summary
        print("📊 Testing dataframe_utils summary...")
        log_debug_summary()
        print("✅ Debug summary logged")
        
        # Test slow operation (this will take a while)
        print("\n⚠️  The next test will take ~5 seconds and trigger timeout detection...")
        await test_slow_operation()
        
        # Test stuck simulation
        print("\n⚠️  The next test simulates a stuck operation...")
        await test_stuck_simulation()
        
        print("\n🎉 All debugging tests completed!")
        print("📝 Check your logs for debugging output")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
    
    finally:
        # Final dataframe_utils summary
        print("\n📊 Final dataframe_utils summary:")
        log_debug_summary()


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run tests
    asyncio.run(main())
