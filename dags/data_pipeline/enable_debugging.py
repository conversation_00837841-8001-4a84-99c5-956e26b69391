#!/usr/bin/env python3
"""
Script to enable debugging for the data pipeline when it gets stuck during rate limiting.

Usage:
1. Set environment variable: export ENABLE_DEBUG_MONITOR=true
2. Run your pipeline
3. If it gets stuck, you can:
   - Check logs for debugging information
   - Send signals (Unix only): kill -USR1 <pid> for stack traces, kill -USR2 <pid> for state dump
   - Call log_debug_summary() programmatically

For Windows users, debugging is automatically enabled through periodic logging.
"""

import os
import sys
import logging
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from dags.data_pipeline.debug.debug_utils import enable_debug_monitoring, log_debug_summary, debug_monitor

logger = logging.getLogger(__name__)


def setup_debugging():
    """Setup debugging environment"""
    # Enable dataframe_utils monitoring
    enable_debug_monitoring()
    
    # Set environment variable for future runs
    os.environ["ENABLE_DEBUG_MONITOR"] = "true"
    
    print("✅ Debug monitoring enabled!")
    print("📊 Monitoring will log system state every 30 seconds")
    print("🔍 HTTP requests, queue operations, and DB operations will be tracked")
    
    if sys.platform != "win32":
        print("🔧 Signal handlers installed:")
        print("   - Send SIGUSR1 to dump stack traces: kill -USR1 <pid>")
        print("   - Send SIGUSR2 to dump current state: kill -USR2 <pid>")
        print(f"   - Current PID: {os.getpid()}")
    else:
        print("🪟 Windows detected - using periodic logging only")
    
    print("\n📝 To manually log dataframe_utils summary, call: log_debug_summary()")
    print("🛑 To disable debugging, call: disable_debug_monitoring()")


def test_debugging():
    """Test the debugging functionality"""
    print("\n🧪 Testing debugging functionality...")
    
    # Test logging
    logger.info("Test log message")
    
    # Test dataframe_utils summary
    log_debug_summary()
    
    print("✅ Debugging test completed!")


def monitor_for_stuck_process(check_interval=60, max_idle_time=300):
    """
    Monitor the process and detect if it seems stuck.
    
    Args:
        check_interval: How often to check (seconds)
        max_idle_time: How long without activity before considering stuck (seconds)
    """
    print(f"\n👀 Starting stuck process monitor...")
    print(f"   - Check interval: {check_interval}s")
    print(f"   - Max idle time: {max_idle_time}s")
    
    last_activity_check = time.time()
    
    try:
        while True:
            time.sleep(check_interval)
            
            current_time = time.time()
            
            # Check if we have recent activity
            has_recent_activity = False
            
            # Check HTTP activity
            if hasattr(debug_monitor, 'last_activity') and debug_monitor.last_activity:
                for activity_type, last_time in debug_monitor.last_activity.items():
                    if current_time - last_time < max_idle_time:
                        has_recent_activity = True
                        break
            
            if not has_recent_activity:
                idle_time = current_time - last_activity_check
                if idle_time > max_idle_time:
                    print(f"Process appears stuck! No activity for {idle_time:.1f}s")
                    print("Forcing dataframe_utils summary...")
                    log_debug_summary()
                    
                    if sys.platform != "win32":
                        print("Dumping stack traces...")
                        debug_monitor.dump_stack_traces()
            else:
                last_activity_check = current_time
                
    except KeyboardInterrupt:
        print("\nMonitor stopped by user")


def main():
    """Main function"""
    print("🐛 Data Pipeline Debugging Utility")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "enable":
            setup_debugging()
        elif command == "test":
            setup_debugging()
            test_debugging()
        elif command == "monitor":
            setup_debugging()
            monitor_for_stuck_process()
        elif command == "summary":
            log_debug_summary()
        elif command == "help":
            print_help()
        else:
            print(f"❌ Unknown command: {command}")
            print_help()
    else:
        print_help()


def print_help():
    """Print help information"""
    print("""
Available commands:
  enable   - Enable debugging monitoring
  test     - Enable debugging and run a test
  monitor  - Enable debugging and monitor for stuck processes
  summary  - Log current dataframe_utils summary
  help     - Show this help message

Examples:
  python enable_debugging.py enable
  python enable_debugging.py monitor
  python enable_debugging.py summary

Environment Variables:
  ENABLE_DEBUG_MONITOR=true  - Enable debugging (set this before running your pipeline)

For stuck processes:
1. Run: python enable_debugging.py enable
2. Start your pipeline
3. If it gets stuck, run: python enable_debugging.py summary
4. Or use signals (Unix): kill -USR1 <pid> or kill -USR2 <pid>
""")


if __name__ == "__main__":
    main()
