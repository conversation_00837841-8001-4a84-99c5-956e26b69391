# coding=utf-8
"""
Debug utilities for tracking async operations, queue states, and HTTP requests.
Helps identify where code gets stuck during rate limiting or other issues.
"""

import asyncio
import functools
import logging
import time
import traceback
from contextlib import asynccontextmanager, contextmanager
from typing import Any, Callable, Optional, Dict, TypeVar, Awaitable
import aiohttp

from dags.data_pipeline.custom_logger import debug_monitor

logger = logging.getLogger(__name__)
F = TypeVar("F", bound=Callable[..., Awaitable[Any]])

def debug_async_function(func_name: Optional[str] = None):
    """
    Decorator to dataframe_utils async functions - tracks execution time and logs entry/exit.
    """
    def decorator(func: F) -> F:
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            debug_monitor.log_task_start(name)
            start_time = time.time()
            
            try:
                logger.debug(f"[DEBUG] Entering {name}")
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"[DEBUG] Exiting {name} (took {duration:.2f}s)")
                debug_monitor.log_task_end(name, success=True)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"[DEBUG] Error in {name} after {duration:.2f}s: {e}")
                debug_monitor.log_task_end(name, success=False)
                raise
        
        return wrapper
    return decorator


def debug_sync_function(func_name: Optional[str] = None):
    """
    Decorator to dataframe_utils sync functions - tracks execution time and logs entry/exit.
    """
    def decorator(func: Callable) -> Callable:
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                logger.debug(f"[DEBUG] Entering {name}")
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"[DEBUG] Exiting {name} (took {duration:.2f}s)")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"[DEBUG] Error in {name} after {duration:.2f}s: {e}")
                raise
        
        return wrapper
    return decorator


@asynccontextmanager
async def debug_queue_operation(queue, operation: str, queue_name: str, *args, **kwargs):
    """
    Context manager to dataframe_utils queue operations (get/put/task_done).
    """
    start_time = time.time()
    initial_size = getattr(queue, 'qsize', lambda: -1)()
    task = asyncio.current_task()
    task_name = task.get_name() if hasattr(task, "get_name") else str(task)
    logger.debug(f"[QUEUE] {operation} by {task_name} on {queue_name} (size: {initial_size})")
    
    try:
        result = None

        if operation == "put":
            result = await queue.put(*args, **kwargs)
        elif operation == "get":
            result = await queue.get()
        elif operation == "task_done":
            queue.task_done()
        else:
            raise ValueError(f"Unsupported operation: {operation}")

        yield result
        
        final_size = getattr(queue, 'qsize', lambda: -1)()
        if final_size == 0 and operation == "get":
            logger.warning(f"[QUEUE] {queue_name} is empty after get.")
        elif final_size == queue.maxsize and operation == "put":
            logger.warning(f"[QUEUE] {queue_name} is full after put.")

        duration = time.time() - start_time
        debug_monitor.log_queue_activity(queue_name, operation, final_size)

        if duration > 5.0:  # Log slow queue operations
            logger.warning(f"[QUEUE] Slow {operation} on {queue_name} by {task_name}: {duration:.2f}s")
        else:
            logger.debug(
                f"[QUEUE] {operation} on {queue_name} completed (size: {final_size}, took {duration:.2f}s)"
            )
            
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            f"[QUEUE] Error in {operation} on {queue_name} by {task_name} after {duration:.2f}s: {e}\n"
            f"{''.join(traceback.format_exc())}"
        )
        raise


@asynccontextmanager
async def debug_http_request(session: aiohttp.ClientSession, method: str, url: str):
    """
    Context manager to dataframe_utils HTTP requests and track rate limiting.
    """
    start_time = time.time()
    
    try:
        logger.debug(f"[HTTP] {method} {url}")
        
        # Yield the session for the actual request
        yield session
        
    except aiohttp.ClientResponseError as e:
        duration = time.time() - start_time
        debug_monitor.log_http_request(method, url, e.status)
        
        if e.status == 429:
            logger.warning(f"[HTTP] Rate limit hit: {method} {url} (took {duration:.2f}s)")
        else:
            logger.error(f"[HTTP] Request failed: {method} {url} - {e.status} (took {duration:.2f}s)")
        raise
        
    except Exception as e:
        duration = time.time() - start_time
        debug_monitor.log_http_request(method, url, None)
        logger.error(f"[HTTP] Request error: {method} {url} after {duration:.2f}s: {e}")
        raise
    
    else:
        duration = time.time() - start_time
        debug_monitor.log_http_request(method, url, 200)  # Assume success
        logger.debug(f"[HTTP] {method} {url} completed (took {duration:.2f}s)")


@contextmanager
def debug_db_operation(operation: str):
    """
    Context manager to dataframe_utils database operations.
    """
    start_time = time.time()
    
    try:
        logger.debug(f"[DB] Starting {operation}")
        yield
        
        duration = time.time() - start_time
        debug_monitor.log_db_operation(operation, duration)
        
        if duration > 10.0:  # Log slow DB operations
            logger.warning(f"[DB] Slow operation: {operation} took {duration:.2f}s")
        else:
            logger.debug(f"[DB] {operation} completed (took {duration:.2f}s)")
            
    except Exception as e:
        duration = time.time() - start_time
        debug_monitor.log_db_operation(operation, duration)
        logger.error(f"[DB] Error in {operation} after {duration:.2f}s: {e}")
        raise


class DebuggedQueue:
    """
    Wrapper around asyncio.Queue that adds debugging capabilities.
    """
    
    def __init__(self, queue: asyncio.Queue, name: str):
        self.queue = queue
        self.name = name
        debug_monitor.register_queue(queue, name)
    
    async def get(self):
        async with debug_queue_operation(self.queue, "get", self.name):
            return await self.queue.get()
    
    async def put(self, item):
        async with debug_queue_operation(self.queue, "put", self.name):
            await self.queue.put(item)
    
    def task_done(self):
        with debug_queue_operation(self.queue, "task_done", self.name):
            self.queue.task_done()
    
    async def join(self):
        async with debug_queue_operation(self.queue, "join", self.name):
            await self.queue.join()
    
    def qsize(self):
        return self.queue.qsize()
    
    def empty(self):
        return self.queue.empty()
    
    def full(self):
        return self.queue.full()


def wrap_queue_with_debug(queue: asyncio.Queue, name: str) -> DebuggedQueue:
    """
    Wrap an existing queue with debugging capabilities.
    """
    return DebuggedQueue(queue, name)


class RateLimitTracker:
    """
    Tracks rate limiting patterns and provides insights.
    """
    
    def __init__(self):
        self.rate_limit_events = []
        self.consecutive_rate_limits = 0
        self.last_rate_limit_time = None
    
    def record_rate_limit(self, url: str, retry_delay: float):
        """Record a rate limit event"""
        current_time = time.time()
        
        # Check if this is consecutive
        if self.last_rate_limit_time and (current_time - self.last_rate_limit_time) < 60:
            self.consecutive_rate_limits += 1
        else:
            self.consecutive_rate_limits = 1
        
        self.last_rate_limit_time = current_time
        
        event = {
            "timestamp": current_time,
            "url": url,
            "retry_delay": retry_delay,
            "consecutive": self.consecutive_rate_limits
        }
        
        self.rate_limit_events.append(event)
        
        # Keep only last 100 events
        if len(self.rate_limit_events) > 100:
            self.rate_limit_events.pop(0)
        
        # Log warning for consecutive rate limits
        if self.consecutive_rate_limits >= 3:
            logger.warning(f"[RATE_LIMIT] {self.consecutive_rate_limits} consecutive rate limits detected. "
                         f"Last delay: {retry_delay}ms. Consider increasing delays.")
    
    def get_rate_limit_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics"""
        if not self.rate_limit_events:
            return {"total_events": 0}
        
        recent_events = [e for e in self.rate_limit_events if time.time() - e["timestamp"] < 300]  # Last 5 minutes
        
        return {
            "total_events": len(self.rate_limit_events),
            "recent_events": len(recent_events),
            "consecutive_rate_limits": self.consecutive_rate_limits,
            "last_rate_limit": self.last_rate_limit_time,
            "avg_retry_delay": sum(e["retry_delay"] for e in recent_events) / len(recent_events) if recent_events else 0
        }


# Global rate limit tracker
rate_limit_tracker = RateLimitTracker()


def log_debug_summary():
    """
    Log a summary of debugging information.
    Call this periodically or when the system seems stuck.
    """
    if not debug_monitor.enabled:
        logger.info("Debug monitoring is disabled. Set ENABLE_DEBUG_MONITOR=true to enable.")
        return
    
    logger.warning("=== DEBUG SUMMARY ===")
    debug_monitor.force_log_state()
    
    # Rate limit stats
    rate_stats = rate_limit_tracker.get_rate_limit_stats()
    logger.warning(f"Rate Limit Stats: {rate_stats}")
    
    logger.warning("=== END DEBUG SUMMARY ===")


def enable_debug_monitoring():
    """Enable dataframe_utils monitoring programmatically"""
    debug_monitor.enabled = True
    debug_monitor.start_monitoring()
    logger.info("Debug monitoring enabled")


def disable_debug_monitoring():
    """Disable dataframe_utils monitoring programmatically"""
    debug_monitor.enabled = False
    debug_monitor.stop()
    logger.info("Debug monitoring disabled")


@asynccontextmanager
async def debug_timeout_detection(operation_name: str, timeout_seconds: int = 300):
    """
    Context manager that detects if an operation takes too long and logs dataframe_utils info.
    Useful for wrapping main processing functions that might get stuck.

    Args:
        operation_name: Name of the operation for logging
        timeout_seconds: How long to wait before logging dataframe_utils info (default: 5 minutes)
    """
    start_time = time.time()
    timeout_task = None

    async def timeout_handler():
        """Handler that runs when timeout is reached"""
        await asyncio.sleep(timeout_seconds)
        elapsed = time.time() - start_time
        logger.debug(f"Operation '{operation_name}' has been running for {elapsed:.1f}s")
        logger.debug("Forcing dataframe_utils summary due to potential stuck operation...")
        log_debug_summary()

        if debug_monitor.enabled:
            debug_monitor.dump_stack_traces()

    try:
        # Start timeout monitoring
        timeout_task = asyncio.create_task(timeout_handler())
        logger.debug(f"Starting operation '{operation_name}' with {timeout_seconds}s timeout detection")

        yield

        # Operation completed successfully
        elapsed = time.time() - start_time
        logger.debug(f"Operation '{operation_name}' completed in {elapsed:.1f}s")

    except Exception as e:
        elapsed = time.time() - start_time
        logger.error(f"Operation '{operation_name}' failed after {elapsed:.1f}s: {e}")

        # Log dataframe_utils info on failure
        if elapsed > 30:  # Only log dataframe_utils info for operations that ran for a while
            logger.warning("Logging dataframe_utils info due to operation failure...")
            log_debug_summary()

        raise

    finally:
        # Cancel timeout monitoring
        if timeout_task and not timeout_task.done():
            timeout_task.cancel()
            try:
                await timeout_task
            except asyncio.CancelledError:
                pass


class StuckDetector:
    """
    Detects when the system appears to be stuck based on various metrics.
    """

    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval
        self.last_http_count = 0
        self.last_db_count = 0
        self.last_check_time = time.time()
        self.stuck_warnings = 0

    def check_for_stuck_state(self) -> bool:
        """
        Check if the system appears to be stuck.
        Returns True if stuck, False otherwise.
        """
        if not debug_monitor.enabled:
            return False

        current_time = time.time()
        time_since_last_check = current_time - self.last_check_time

        if time_since_last_check < self.check_interval:
            return False

        # Check for progress indicators
        current_http_count = debug_monitor.http_request_count
        current_db_count = debug_monitor.db_operation_count

        http_progress = current_http_count - self.last_http_count
        db_progress = current_db_count - self.last_db_count

        # Check queue activity
        queue_activity = False
        for queue_name, state in debug_monitor.queue_states.items():
            last_activity = state.get("last_activity", 0)
            if current_time - last_activity < self.check_interval:
                queue_activity = True
                break

        # Determine if stuck
        is_stuck = (
            http_progress == 0 and
            db_progress == 0 and
            not queue_activity and
            time_since_last_check > self.check_interval
        )

        if is_stuck:
            self.stuck_warnings += 1
            logger.warning(f"System appears stuck! No progress in {time_since_last_check:.1f}s")
            logger.warning(f"HTTP requests: {http_progress}, DB operations: {db_progress}, Queue activity: {queue_activity}")
            logger.warning(f"Stuck warnings: {self.stuck_warnings}")

            # Force dataframe_utils summary every 3rd stuck warning
            if self.stuck_warnings % 3 == 0:
                log_debug_summary()

        else:
            # Reset stuck warnings if we see progress
            if self.stuck_warnings > 0:
                logger.info(f"✅ System resumed activity after {self.stuck_warnings} stuck warnings")
                self.stuck_warnings = 0

        # Update counters
        self.last_http_count = current_http_count
        self.last_db_count = current_db_count
        self.last_check_time = current_time

        return is_stuck


# Global stuck detector
stuck_detector = StuckDetector()


async def periodic_stuck_check():
    """
    Periodic task to check for stuck state.
    Run this as a background task in your main application.
    """
    while True:
        try:
            stuck_detector.check_for_stuck_state()
            await asyncio.sleep(stuck_detector.check_interval)
        except asyncio.CancelledError:
            logger.info("Periodic stuck check cancelled")
            break
        except Exception as e:
            logger.error(f"Error in periodic stuck check: {e}")
            await asyncio.sleep(60)  # Wait a bit before retrying
