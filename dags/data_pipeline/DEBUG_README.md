# Debugging Guide for Rate Limiting Issues

This guide helps you debug when the data pipeline gets stuck during HTTP 429 (rate limiting) errors or other async operation issues.

## Quick Start

### 1. Enable Debugging
```bash
# Set environment variable
export ENABLE_DEBUG_MONITOR=true

# Or use the helper script
python dags/data_pipeline/enable_debugging.py enable
```

### 2. Run Your Pipeline
Start your normal pipeline process. The debugging monitor will automatically:
- Log system state every 30 seconds
- Track HTTP requests and rate limiting
- Monitor queue operations and sizes
- Track database operations
- Monitor async task states

### 3. When Process Gets Stuck

#### Option A: Check Logs
Look for debugging output in your logs:
```
=== DEBUGGING MONITOR REPORT (Uptime: 120.5s) ===
Event Loop: {'running': True, 'closed': False, 'debug': False, 'task_count': 15}
HTTP Requests: 245
Rate Limits Hit: 12
DB Operations: 89
Active Tasks: 8
Active Queues: 5
Queue States: {'queue_issues': {'size': 150, 'last_activity': 5.2, 'total_items': 1200}}
...
```

#### Option B: Force Debug Summary
```bash
python dags/data_pipeline/enable_debugging.py summary
```

#### Option C: Use Signals (Unix/Linux/WSL only)
```bash
# Get process ID
ps aux | grep python

# Dump stack traces
kill -USR1 <pid>

# Dump current state
kill -USR2 <pid>
```

## Debugging Features

### 1. HTTP Request Monitoring
- Tracks all HTTP requests
- Counts rate limit hits (429 errors)
- Monitors consecutive rate limits
- Logs slow requests

### 2. Queue Operation Monitoring
- Tracks get/put/task_done operations
- Monitors queue sizes
- Detects large queues (>100 items)
- Logs slow queue operations (>5s)

### 3. Database Operation Monitoring
- Tracks all async database operations
- Monitors operation duration
- Logs slow operations (>10s)

### 4. Task Monitoring
- Tracks async task lifecycle
- Monitors running tasks
- Detects long-running tasks

### 5. Memory and System Monitoring
- Memory usage tracking
- Thread count monitoring
- Event loop state

## Common Issues and Solutions

### Issue 1: High Rate Limit Count
**Symptoms:** `Rate Limits Hit: 50+` in debug output
**Solutions:**
- Increase delays between requests
- Reduce concurrent request count
- Check if retry delays are appropriate

### Issue 2: Large Queue Sizes
**Symptoms:** `Queue States` showing large sizes (>100)
**Solutions:**
- Check if consumers are processing items
- Look for stuck task_done() calls
- Verify database operations aren't blocking

### Issue 3: Stuck Database Operations
**Symptoms:** High DB operation count but no progress
**Solutions:**
- Check for database deadlocks
- Verify connection pool isn't exhausted
- Look for long-running transactions

### Issue 4: Memory Issues
**Symptoms:** High memory usage in debug output
**Solutions:**
- Check for memory leaks in DataFrame processing
- Verify proper cleanup of resources
- Monitor queue sizes for excessive buildup

## Advanced Debugging

### 1. Custom Debug Points
Add debugging to your own functions:
```python
from debug_utils import debug_async_function, debug_queue_operation

@debug_async_function("my_function")
async def my_function():
    # Your code here
    pass

# For queue operations
async with debug_queue_operation(queue, "get", "my_queue"):
    item = await queue.get()
```

### 2. Manual State Logging
```python
from debug_utils import log_debug_summary
log_debug_summary()  # Force immediate dataframe_utils output
```

### 3. Rate Limit Analysis
```python
from debug_utils import rate_limit_tracker
stats = rate_limit_tracker.get_rate_limit_stats()
print(f"Rate limit stats: {stats}")
```

## Configuration

### Environment Variables
- `ENABLE_DEBUG_MONITOR=true` - Enable debugging
- `LOG_FILE_PATH` - Directory for log files

### Debug Monitor Settings
The monitor logs every 30 seconds by default. You can modify this in `custom_logger.py`:
```python
debug_monitor = DebuggingMonitor(enabled=True, log_interval=60)  # 60 seconds
```

## Performance Impact

The debugging system is designed to have minimal performance impact:
- Monitoring runs in a separate thread
- Queue operations add minimal overhead
- HTTP tracking is lightweight
- Can be completely disabled by setting `ENABLE_DEBUG_MONITOR=false`

## Troubleshooting the Debugger

### Debugger Not Working
1. Check environment variable: `echo $ENABLE_DEBUG_MONITOR`
2. Verify imports are working
3. Check for error messages in logs

### No Debug Output
1. Ensure logging level allows WARNING messages
2. Check if custom_logger is properly configured
3. Verify the monitoring thread is running

### Signal Handlers Not Working (Unix)
1. Ensure you're not on Windows
2. Check if process has signal handling permissions
3. Verify PID is correct

## Files Added/Modified

### New Files
- `debug_utils.py` - Core debugging utilities
- `enable_debugging.py` - Helper script for enabling debugging
- `DEBUG_README.md` - This documentation

### Modified Files
- `custom_logger.py` - Added DebuggingMonitor class
- `utility_code.py` - Added debugging decorators to key functions

## Example Debug Session

```bash
# 1. Enable debugging
export ENABLE_DEBUG_MONITOR=true

# 2. Start pipeline
python your_pipeline.py

# 3. If it gets stuck, check status
python enable_debugging.py summary

# 4. Look for issues in output:
# - High rate limit count
# - Large queue sizes
# - Stuck database operations
# - Long-running tasks

# 5. Take action based on findings
```

This debugging system should help you identify exactly where the code gets stuck during rate limiting scenarios and provide actionable insights for resolution.
