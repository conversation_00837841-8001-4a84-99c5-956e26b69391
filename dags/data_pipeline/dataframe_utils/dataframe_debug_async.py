import os
import time
import asyncio
import logging
from logging.handlers import TimedRotatingFileHandler

import pandas as pd
from pathlib import Path
from typing import Optional, Dict, Any, Union, List, Callable, Awaitable
from datetime import datetime
from contextlib import asynccontextmanager
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, ProcessPoolExecutor
from dataclasses import dataclass, field
from enum import Enum
import weakref



class SaveFormat(Enum):
    """Supported file formats."""
    CSV = "csv"
    EXCEL = "xlsx"
    PARQUET = "parquet"
    FEATHER = "feather"
    JSON = "json"


class Priority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class SaveTask:
    """Represents a save task with metadata."""
    df: pd.DataFrame
    file_path: Path
    file_format: SaveFormat
    column_formats: Dict[str, Any] = field(default_factory=dict)
    priority: Priority = Priority.NORMAL
    timestamp: datetime = field(default_factory=datetime.now)
    callback: Optional[Callable[[bool, Optional[str]], Awaitable[None]]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        self.df = self.df.copy()  # Ensure we have a copy


class AsyncDataframeDebugger:
    """
    An async-compatible DataFrame debugger with advanced features.

    Features:
    - Async/await support with asyncio
    - Priority-based task queue
    - Multiple file format support
    - Concurrent processing with thread/process pools
    - Progress callbacks and real-time monitoring
    - Memory-efficient processing for large DataFrames
    - Batch operations with transaction-like behavior
    - Automatic compression and optimization
    """

    def __init__(
            self,
            path: Optional[Union[str, Path]] = None,
            max_workers: int = 4,
            max_retries: int = 5,
            retry_delay: float = 1.0,
            create_directory: bool = True,
            log_level: int = logging.INFO,
            use_process_pool: bool = False,
            max_queue_size: int = 1000,
            auto_compress: bool = True,
            chunk_size: int = 10000,
            enable_progress_callback: bool = True
    ):
        """
        Initialize the AsyncDataFrameDebugger.

        Args:
            path: Directory path where files will be saved.
            max_workers: Maximum number of concurrent workers.
            max_retries: Maximum retry attempts for failed saves.
            retry_delay: Initial delay between retries.
            create_directory: Whether to create directory if it doesn't exist.
            log_level: Logging level.
            use_process_pool: Use ProcessPoolExecutor instead of ThreadPoolExecutor.
            max_queue_size: Maximum number of tasks in queue.
            auto_compress: Automatically compress large files.
            chunk_size: Chunk size for processing large DataFrames.
            enable_progress_callback: Enable progress reporting.
        """
        # Setup logging
        self.logger = self._setup_logger(log_level)

        # Setup path
        self.path = self._setup_path(path, create_directory)

        # Configuration
        self.max_workers = max(1, max_workers)
        self.max_retries = max(1, max_retries)
        self.retry_delay = max(0.1, retry_delay)
        self.use_process_pool = use_process_pool
        self.max_queue_size = max_queue_size
        self.auto_compress = auto_compress
        self.chunk_size = chunk_size
        self.enable_progress_callback = enable_progress_callback

        # Async components
        self.task_queue = asyncio.PriorityQueue(maxsize=max_queue_size)
        self.active_tasks = set()
        self.executor = None
        self.is_running = False
        self._shutdown_event = asyncio.Event()
        self._worker_tasks = []

        # Statistics and monitoring
        self.stats = {
            'files_saved': 0,
            'files_failed': 0,
            'total_retries': 0,
            'bytes_processed': 0,
            'avg_processing_time': 0.0,
            'queue_high_water_mark': 0
        }

        # Progress tracking
        self.progress_callbacks = []
        self._task_counter = 0

        # Weak reference tracking for cleanup
        self._instances = weakref.WeakSet()
        self._instances.add(self)

        self.logger.info(f"AsyncDataframeDebugger initialized with {max_workers} workers")

    def _setup_logger(self, log_level: int) -> logging.Logger:
        """Setup async-aware logger."""
        logger = logging.getLogger(f"{__name__}.async.{id(self)}")
        logger.setLevel(log_level)

        if not logger.handlers:
            log_dir = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
            os.makedirs(log_dir, exist_ok=True)
            log_file_path = os.path.join(log_dir, f"debugger_{id(self)}.log")
            # Timed rotating file handler: rotates daily, keeps 7 days of logs
            handler = TimedRotatingFileHandler(
                filename=log_file_path,
                when="midnight",
                interval=1,
                backupCount=7,
                encoding="utf-8"
            )

            # handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(task_name)s] %(message)s',
                defaults={'task_name': 'main'}
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _setup_path(self, path: Optional[Union[str, Path]], create_directory: bool) -> Path:
        """Setup and validate the output path."""
        if path is None:
            path = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
        else:
            path = Path(path)

        if create_directory:
            try:
                path.mkdir(parents=True, exist_ok=True)
            except OSError as e:
                self.logger.warning(f"Could not create directory {path}: {e}")

        if not path.exists():
            raise FileNotFoundError(f"Output directory does not exist: {path}")

        return path

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_value, traceback_value):
        """Async context manager exit."""
        await self.stop()

    async def start(self):
        """Start the async debugger."""
        if self.is_running:
            return

        self.is_running = True
        self._shutdown_event.clear()

        # Create executor
        if self.use_process_pool:
            self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        else:
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # Start worker tasks
        for i in range(self.max_workers):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self._worker_tasks.append(task)

        self.logger.info(f"Started {self.max_workers} async workers")

    async def stop(self, timeout: float = 30.0):
        """Stop the async debugger with graceful shutdown."""
        if not self.is_running:
            return

        self.logger.info("Stopping AsyncDataframeDebugger...")

        # Signal shutdown
        self._shutdown_event.set()

        # Wait for current tasks to complete
        if self.active_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.active_tasks, return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                self.logger.warning("Some tasks did not complete within timeout")

        # Cancel worker tasks
        for task in self._worker_tasks:
            if not task.done():
                task.cancel()

        if self._worker_tasks:
            await asyncio.gather(*self._worker_tasks, return_exceptions=True)

        # Shutdown executor
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = None

        self.is_running = False
        self._worker_tasks.clear()
        self.active_tasks.clear()

        self.logger.info(f"AsyncDataframeDebugger stopped. Final stats: {self.stats}")

    async def save_dataframe(
            self,
            df: pd.DataFrame,
            filename: Optional[str] = None,
            file_format: Optional[Union[str, SaveFormat]] = None,
            column_formats: Optional[Dict[str, Any]] = None,
            priority: Priority = Priority.NORMAL,
            callback: Optional[Callable[[bool, Optional[str]], Awaitable[None]]] = None,
            **metadata
    ) -> str:
        """
        Queue a DataFrame for async saving.

        Args:
            df: DataFrame to save.
            filename: Output filename (auto-generated if None).
            file_format: File format (inferred from filename if None).
            column_formats: Formatting options for supported formats.
            priority: Task priority.
            callback: Async callback function called with (success, error_message).
            **metadata: Additional metadata for the task.

        Returns:
            str: Task ID for tracking.

        Raises:
            ValueError: If DataFrame is empty or invalid format.
            asyncio.QueueFull: If queue is full.
        """
        if not self.is_running:
            await self.start()

        if df is None or df.empty:
            raise ValueError("Cannot save empty or None DataFrame")

        # Generate filename and determine format
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"dataframe_{timestamp}.xlsx"

        if file_format is None:
            file_extension = Path(filename).suffix.lower().lstrip('.')
            if not file_extension:
                file_extension = 'xlsx'
                filename += f'.{file_extension}'
        else:
            if isinstance(file_format, str):
                file_format = SaveFormat(file_format.lower())
            file_extension = file_format.value
            if not filename.endswith(f'.{file_extension}'):
                filename = Path(filename).stem + f'.{file_extension}'

        # Validate format
        try:
            save_format = SaveFormat(file_extension)
        except ValueError:
            raise ValueError(f"Unsupported file format: {file_extension}")

        file_path = self.path / filename

        # Create task
        task_id = f"task_{self._task_counter}_{int(time.time() * 1000)}"
        self._task_counter += 1

        task = SaveTask(
            df=df,
            file_path=file_path,
            file_format=save_format,
            column_formats=column_formats or {},
            priority=priority,
            callback=callback,
            metadata={'task_id': task_id, **metadata}
        )

        # Queue task (priority queue uses negative values for higher priority)
        priority_value = -priority.value
        await self.task_queue.put((priority_value, task_id, task))

        # Update stats
        self.stats['queue_high_water_mark'] = max(
            self.stats['queue_high_water_mark'],
            self.task_queue.qsize()
        )

        await self._notify_progress("queued", task_id, filename)
        self.logger.debug(f"Queued task {task_id}: {filename}")

        return task_id

    async def _worker(self, worker_name: str):
        """Async worker to process save tasks."""
        self.logger.debug(f"Worker {worker_name} started")

        while not self._shutdown_event.is_set():
            try:
                # Wait for task with timeout to allow periodic shutdown checks
                try:
                    priority, task_id, task = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # Process task
                process_task = asyncio.create_task(
                    self._process_task(task, worker_name)
                )
                self.active_tasks.add(process_task)

                try:
                    await process_task
                finally:
                    self.active_tasks.discard(process_task)
                    self.task_queue.task_done()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")

        self.logger.debug(f"Worker {worker_name} stopped")

    async def _process_task(self, task: SaveTask, worker_name: str):
        """Process a single save task."""
        task_id = task.metadata.get('task_id', 'unknown')
        start_time = time.time()

        await self._notify_progress("processing", task_id, str(task.file_path.name))

        try:
            # Process DataFrame in executor to avoid blocking
            loop = asyncio.get_event_loop()

            success = await loop.run_in_executor(
                self.executor,
                self._save_with_retries_sync,
                task
            )

            processing_time = time.time() - start_time

            if success:
                self.stats['files_saved'] += 1
                self.stats['bytes_processed'] += self._estimate_dataframe_size(task.df)
                await self._notify_progress("completed", task_id, str(task.file_path.name))
                self.logger.info(f"Successfully saved {task.file_path} in {processing_time:.2f}s")
            else:
                self.stats['files_failed'] += 1
                await self._notify_progress("failed", task_id, str(task.file_path.name))
                self.logger.error(f"Failed to save {task.file_path}")

            # Update average processing time
            total_files = self.stats['files_saved'] + self.stats['files_failed']
            if total_files > 0:
                self.stats['avg_processing_time'] = (
                        (self.stats['avg_processing_time'] * (total_files - 1) + processing_time) / total_files
                )

            # Call callback if provided
            if task.callback:
                try:
                    await task.callback(success, None if success else "Save failed")
                except Exception as e:
                    self.logger.warning(f"Callback error for task {task_id}: {e}")

        except Exception as e:
            self.stats['files_failed'] += 1
            await self._notify_progress("error", task_id, str(task.file_path.name))
            self.logger.error(f"Task {task_id} failed with error: {e}")

            if task.callback:
                try:
                    await task.callback(False, str(e))
                except Exception as callback_error:
                    self.logger.warning(f"Callback error for task {task_id}: {callback_error}")

    def _save_with_retries_sync(self, task: SaveTask) -> bool:
        """Synchronous save with retries (runs in executor)."""
        df = self._preprocess_dataframe(task.df)

        for attempt in range(self.max_retries):
            try:
                if task.file_format == SaveFormat.CSV:
                    self._save_csv_sync(df, task.file_path)
                elif task.file_format == SaveFormat.EXCEL:
                    self._save_excel_sync(df, task.file_path, task.column_formats)
                elif task.file_format == SaveFormat.PARQUET:
                    self._save_parquet_sync(df, task.file_path)
                elif task.file_format == SaveFormat.FEATHER:
                    self._save_feather_sync(df, task.file_path)
                elif task.file_format == SaveFormat.JSON:
                    self._save_json_sync(df, task.file_path)
                else:
                    raise ValueError(f"Unsupported format: {task.file_format}")

                # Apply compression if enabled
                if self.auto_compress and task.file_path.stat().st_size > 1024 * 1024:  # 1MB
                    self._compress_file(task.file_path)

                return True

            except Exception as e:
                self.stats['total_retries'] += 1

                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt)
                    time.sleep(delay)
                else:
                    return False

        return False

    def _preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess DataFrame for saving."""
        df = df.copy()

        # Handle timezone-aware datetimes
        datetime_cols = df.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64[ns]']).columns
        for col in datetime_cols:
            if hasattr(df[col].dtype, 'tz') and df[col].dtype.tz is not None:
                df[col] = df[col].dt.tz_localize(None)

        # Handle complex objects
        object_cols = df.select_dtypes(include=['object']).columns
        for col in object_cols:
            if df[col].apply(lambda x: isinstance(x, (list, dict))).any():
                df[col] = df[col].astype(str)

        return df

    def _save_csv_sync(self, df: pd.DataFrame, file_path: Path):
        """Save DataFrame to CSV."""
        df.to_csv(file_path, index=False, encoding='utf-8')

    def _save_excel_sync(self, df: pd.DataFrame, file_path: Path, column_formats: Dict[str, Any]):
        """Save DataFrame to Excel with formatting."""
        with pd.ExcelWriter(file_path, engine="xlsxwriter") as writer:
            df.to_excel(writer, index=False, sheet_name="Data")

            worksheet = writer.sheets['Data']
            workbook = writer.book

            # Apply formatting
            wrap_format = workbook.add_format({'text_wrap': True})
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4F81BD',
                'font_color': 'white'
            })

            # Set column widths and formats
            for i, col in enumerate(df.columns):
                width = column_formats.get(col, 15)
                worksheet.set_column(i, i, width)
                worksheet.write(0, i, col, header_format)

            if not df.empty:
                worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

    def _save_parquet_sync(self, df: pd.DataFrame, file_path: Path):
        """Save DataFrame to Parquet."""
        df.to_parquet(file_path, index=False)

    def _save_feather_sync(self, df: pd.DataFrame, file_path: Path):
        """Save DataFrame to Feather."""
        df.to_feather(file_path)

    def _save_json_sync(self, df: pd.DataFrame, file_path: Path):
        """Save DataFrame to JSON."""
        df.to_json(file_path, orient='records', date_format='iso')

    def _compress_file(self, file_path: Path):
        """Compress file using gzip."""
        import gzip
        import shutil

        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)

        # Replace original with compressed version
        file_path.unlink()
        compressed_path.rename(file_path.with_suffix(file_path.suffix + '.gz'))

    def _estimate_dataframe_size(self, df: pd.DataFrame) -> int:
        """Estimate DataFrame size in bytes."""
        return df.memory_usage(deep=True).sum()

    async def _notify_progress(self, status: str, task_id: str, filename: str):
        """Notify progress callbacks."""
        if not self.enable_progress_callback or not self.progress_callbacks:
            return

        progress_data = {
            'status': status,
            'task_id': task_id,
            'filename': filename,
            'timestamp': datetime.now(),
            'stats': self.stats.copy()
        }

        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress_data)
                else:
                    callback(progress_data)
            except Exception as e:
                self.logger.warning(f"Progress callback error: {e}")

    def add_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add a progress callback function."""
        self.progress_callbacks.append(callback)

    def remove_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Remove a progress callback function."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)

    async def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for all queued tasks to complete."""
        try:
            if timeout is None:
                await self.task_queue.join()
                return True
            else:
                await asyncio.wait_for(self.task_queue.join(), timeout=timeout)
                return True
        except asyncio.TimeoutError:
            return False

    async def get_queue_size(self) -> int:
        """Get current queue size."""
        return self.task_queue.qsize()

    async def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        stats = self.stats.copy()
        stats['queue_size'] = await self.get_queue_size()
        stats['active_tasks'] = len(self.active_tasks)
        stats['is_running'] = self.is_running
        return stats

    @asynccontextmanager
    async def batch_save(self):
        """
        Async context manager for batch operations.

        Usage:
            async with debugger.batch_save():
                await debugger.save_dataframe(df1, "file1.xlsx")
                await debugger.save_dataframe(df2, "file2.csv")
            # All files will be saved before exiting
        """
        try:
            yield self
        finally:
            await self.wait_for_completion()

    async def save_multiple(
            self,
            dataframes: List[tuple],  # [(df, filename, options), ...]
            priority: Priority = Priority.NORMAL
    ) -> List[str]:
        """
        Save multiple DataFrames concurrently.

        Args:
            dataframes: List of tuples (df, filename, options_dict)
            priority: Priority for all tasks

        Returns:
            List of task IDs
        """
        tasks = []
        for item in dataframes:
            if len(item) == 2:
                df, filename = item
                options = {}
            else:
                df, filename, options = item

            task_id = await self.save_dataframe(
                df, filename, priority=priority, **options
            )
            tasks.append(task_id)

        return tasks


# Utility functions for common use cases
async def quick_save_async(
        df: pd.DataFrame,
        filename: Optional[str] = None,
        path: Optional[str] = None,
        file_format: Optional[str] = None,
        **kwargs
) -> bool:
    """
    Quick async utility to save a DataFrame.

    Args:
        df: DataFrame to save
        filename: Output filename
        path: Output directory
        file_format: File format
        **kwargs: Additional options

    Returns:
        bool: True if successful
    """
    try:
        async with AsyncDataframeDebugger(path=path, **kwargs) as debugger:
            await debugger.save_dataframe(df, filename, file_format)
            return await debugger.wait_for_completion(timeout=60.0)
    except Exception as e:
        logging.error(f"Quick async save failed: {e}")
        return False


async def save_dataframes_parallel(
        dataframes: Dict[str, pd.DataFrame],
        path: Optional[str] = None,
        file_format: str = "xlsx",
        max_workers: int = 4
) -> Dict[str, bool]:
    """
    Save multiple DataFrames in parallel.

    Args:
        dataframes: Dictionary of {filename: DataFrame}
        path: Output directory
        file_format: File format for all files
        max_workers: Number of parallel workers

    Returns:
        Dict mapping filenames to success status
    """
    results = {}

    async def save_callback(success: bool, error: Optional[str], filename: str):
        results[filename] = success
        if not success:
            logging.error(f"Failed to save {filename}: {error}")

    async with AsyncDataframeDebugger(path=path, max_workers=max_workers) as debugger:
        tasks = []
        for filename, df in dataframes.items():
            callback = lambda s, e, f=filename: asyncio.create_task(save_callback(s, e, f))
            task_id = await debugger.save_dataframe(
                df, filename, file_format, callback=callback
            )
            tasks.append(task_id)

        await debugger.wait_for_completion()

    return results


# Example usage
async def main():
    """Example usage of AsyncDataframeDebugger."""
    import numpy as np

    # Create sample data
    sample_df = pd.DataFrame({
        'id': range(1, 1001),
        'name': [f'Item_{i}' for i in range(1, 1001)],
        'value': np.random.randn(1000),
        'timestamp': pd.date_range('2024-01-01', periods=1000, freq='h'),
        'category': np.random.choice(['A', 'B', 'C', 'D'], 1000)
    })

    # Progress callback
    async def progress_callback(data):
        print(f"Task {data['task_id']}: {data['status']} - {data['filename']}")

    # Example 1: Basic async usage
    async with AsyncDataframeDebugger(max_workers=4) as debugger:
        debugger.add_progress_callback(progress_callback)

        # Save multiple formats
        task1 = await debugger.save_dataframe(sample_df, "sample.xlsx", "xlsx")
        task2 = await debugger.save_dataframe(sample_df, "sample.csv", "csv")
        task3 = await debugger.save_dataframe(sample_df, "sample.parquet", "parquet")

        # Wait for completion
        await debugger.wait_for_completion()

        # Get final stats
        stats = await debugger.get_stats()
        print(f"Final stats: {stats}")

    # Example 2: Batch operations
    async with AsyncDataframeDebugger() as debugger:
        async with debugger.batch_save():
            for i in range(5):
                subset_df = sample_df.iloc[i * 200:(i + 1) * 200]
                await debugger.save_dataframe(
                    subset_df,
                    f"batch_{i}.xlsx",
                    priority=Priority.HIGH
                )

    # Example 3: Parallel save utility
    dataframes = {
        f"parallel_{i}.xlsx": sample_df.iloc[i * 100:(i + 1) * 100]
        for i in range(10)
    }

    results = await save_dataframes_parallel(dataframes, max_workers=6)
    print(f"Parallel save results: {results}")


if __name__ == "__main__":
    asyncio.run(main())