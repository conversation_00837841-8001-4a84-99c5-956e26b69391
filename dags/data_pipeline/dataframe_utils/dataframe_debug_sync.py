import os
import csv
import time
import queue
import atexit
import logging
from logging.handlers import TimedRotatingFileHandler

import pandas as pd
from queue import Queue, Empty
from threading import Thread, Event
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime
from contextlib import contextmanager


class DataframeDebugger:
    """
    A thread-safe class to debug and save DataFrame objects to CSV or Excel files.

    Features:
    - Asynchronous file saving using a background worker thread
    - Automatic retry mechanism with exponential backoff
    - Support for both CSV and Excel formats
    - Configurable column formatting for Excel files
    - Thread-safe operations with proper cleanup
    - Comprehensive logging and error handling
    """

    def __init__(
            self,
            path: Optional[Union[str, Path]] = None,
            max_retries: int = 5,
            retry_delay: float = 2.0,
            create_directory: bool = True,
            log_level: int = logging.INFO
    ):
        """
        Initialize the DataFrameDebugger.

        Args:
            path: Directory path where files will be saved. Defaults to system temp directory.
            max_retries: Maximum number of retry attempts for failed saves.
            retry_delay: Initial delay between retries (exponential backoff applied).
            create_directory: Whether to create the directory if it doesn't exist.
            log_level: Logging level for the debugger.
        """
        # Setup logging
        self.logger = self._setup_logger(log_level)

        # Setup path
        self.path = self._setup_path(path, create_directory)

        # Configuration
        self.max_retries = max(1, max_retries)
        self.retry_delay = max(0.1, retry_delay)

        # Threading components
        self.queue = Queue()
        self.stop_event = Event()
        self.worker_thread = Thread(target=self._worker, daemon=True)
        self.worker_thread.start()

        # Statistics
        self.stats = {
            'files_saved': 0,
            'files_failed': 0,
            'total_retries': 0
        }

        # Register cleanup
        atexit.register(self.stop)
        self.logger.info(f"DataframeDebugger initialized with path: {self.path}")

    def _setup_logger(self, log_level: int) -> logging.Logger:
        """Setup logger for the debugger."""
        logger = logging.getLogger(f"{__name__}.{id(self)}")
        logger.setLevel(log_level)

        if not logger.handlers:
            # Ensure log directory exists
            log_dir = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp/dataframe_debug')
            os.makedirs(log_dir, exist_ok=True)
            log_file_path = os.path.join(log_dir, f"debugger_{id(self)}.log")
            # Timed rotating file handler: rotates daily, keeps 7 days of logs
            handler = TimedRotatingFileHandler(
                filename=log_file_path,
                when="midnight",
                interval=1,
                backupCount=7,
                encoding="utf-8"
            )
            # handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _setup_path(self, path: Optional[Union[str, Path]], create_directory: bool) -> Path:
        """Setup and validate the output path."""
        if path is None:
            path = Path("c:/vishal/log") if os.name == 'nt' else Path('/tmp')
        else:
            path = Path(path)

        if create_directory:
            try:
                path.mkdir(parents=True, exist_ok=True)
            except OSError as e:
                self.logger.warning(f"Could not create directory {path}: {e}")

        if not path.exists():
            raise FileNotFoundError(f"Output directory does not exist: {path}")

        if not path.is_dir():
            raise NotADirectoryError(f"Path is not a directory: {path}")

        return path

    def __enter__(self):
        """Enter the runtime context."""
        return self

    def __exit__(self, exc_type, exc_value, traceback_value):
        """Exit the runtime context."""
        self.stop()

    def debug_dataframe(
            self,
            df: pd.DataFrame,
            filename: Optional[str] = None,
            column_formats: Optional[Dict[str, Any]] = None,
            file_format: Optional[str] = None
    ) -> bool:
        """
        Queue a DataFrame for saving.

        Args:
            df: The DataFrame to save.
            filename: Name of the output file. Auto-generated if None.
            column_formats: Column formatting options for Excel files.
            file_format: File format ('csv' or 'xlsx'). Inferred from filename if None.

        Returns:
            bool: True if successfully queued, False otherwise.
        """
        try:
            if df is None or df.empty:
                self.logger.warning("Cannot save empty or None DataFrame")
                return False

            # Generate filename if not provided
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"dataframe_{timestamp}.xlsx"

            # Determine file format
            if file_format is None:
                file_extension = Path(filename).suffix.lower()
                if not file_extension:
                    file_extension = '.xlsx'
                    filename += file_extension
            else:
                file_extension = f".{file_format.lower().lstrip('.')}"
                if not filename.endswith(file_extension):
                    filename = Path(filename).stem + file_extension

            # Validate file format
            if file_extension not in ['.csv', '.xlsx']:
                raise ValueError(f"Unsupported file format: {file_extension}")

            file_path = self.path / filename
            column_formats = column_formats or {}

            # Queue the task
            task = {
                'df': df.copy(),
                'file_path': file_path,
                'file_extension': file_extension,
                'column_formats': column_formats,
                'timestamp': datetime.now()
            }

            self.queue.put(task)
            self.logger.debug(f"Queued DataFrame save task: {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to queue DataFrame save task: {e}")
            return False

    def _worker(self):
        """Background worker thread to process save tasks."""
        self.logger.debug("Worker thread started")

        while not self.stop_event.is_set():
            try:
                # Use timeout to allow periodic checking of stop_event
                task = self.queue.get(timeout=1.0)

                if task is None:  # Sentinel value to stop
                    break

                success = self._save_with_retries(task)
                if success:
                    self.stats['files_saved'] += 1
                else:
                    self.stats['files_failed'] += 1

                self.queue.task_done()

            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"Unexpected error in worker thread: {e}")
                self.stats['files_failed'] += 1

        self.logger.debug("Worker thread stopped")

    def _save_with_retries(self, task: Dict[str, Any]) -> bool:
        """
        Save DataFrame with retry logic.

        Args:
            task: Task dictionary containing save parameters.

        Returns:
            bool: True if save was successful, False otherwise.
        """
        df = task['df']
        file_path = task['file_path']
        file_extension = task['file_extension']
        column_formats = task['column_formats']

        # Preprocess DataFrame
        df = self._preprocess_dataframe(df)

        for attempt in range(self.max_retries):
            try:
                if file_extension == '.csv':
                    self._save_csv(df, file_path)
                elif file_extension == '.xlsx':
                    self._save_excel(df, file_path, column_formats)
                else:
                    raise ValueError(f"Unsupported file extension: {file_extension}")

                self.logger.info(f"Successfully saved DataFrame to {file_path}")
                return True

            except Exception as e:
                self.stats['total_retries'] += 1

                if attempt < self.max_retries - 1:
                    delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed for {file_path}: {e}. "
                        f"Retrying in {delay:.1f} seconds..."
                    )
                    time.sleep(delay)
                else:
                    self.logger.error(f"Failed to save {file_path} after {self.max_retries} attempts: {e}")

        return False

    def _preprocess_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Preprocess DataFrame before saving.

        Args:
            df: Input DataFrame.

        Returns:
            pd.DataFrame: Preprocessed DataFrame.
        """
        df = df.copy()

        # Handle timezone-aware datetimes
        datetime_cols = df.select_dtypes(include=['datetime64[ns, UTC]', 'datetime64[ns]']).columns
        for col in datetime_cols:
            if df[col].dt.tz is not None:
                df[col] = df[col].dt.tz_localize(None)
                self.logger.debug(f"Converted timezone-aware column '{col}' to naive datetime")

        # Handle other problematic data types
        object_cols = df.select_dtypes(include=['object']).columns
        for col in object_cols:
            # Convert lists/dicts to strings for CSV compatibility
            if df[col].apply(lambda x: isinstance(x, (list, dict))).any():
                df[col] = df[col].astype(str)
                self.logger.debug(f"Converted complex objects in column '{col}' to strings")

        return df

    def _save_csv(self, df: pd.DataFrame, file_path: Path):
        """Save DataFrame to CSV file."""
        df.to_csv(
            file_path,
            index=False,
            quotechar='"',
            quoting=csv.QUOTE_MINIMAL,
            encoding='utf-8'
        )

    def _save_excel(self, df: pd.DataFrame, file_path: Path, column_formats: Dict[str, Any]):
        """Save DataFrame to Excel file with formatting."""
        with pd.ExcelWriter(file_path, engine="xlsxwriter") as writer:
            df.to_excel(writer, index=False, sheet_name="Data")

            worksheet = writer.sheets['Data']
            workbook = writer.book

            # Default formats
            wrap_format = workbook.add_format({'text_wrap': True})
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#4F81BD',
                'font_color': 'white',
                'border': 1
            })

            # Calculate column widths
            column_widths = self._calculate_column_widths(df)

            # Apply formatting
            for i, col in enumerate(df.columns):
                # Set column width
                width = column_formats.get(col, column_widths.get(col, 15))
                worksheet.set_column(i, i, width)

                # Apply wrap format for specific columns
                if col in column_formats.get('wrap_columns', []):
                    worksheet.set_column(i, i, width, wrap_format)

            # Format headers
            for i, col in enumerate(df.columns):
                worksheet.write(0, i, col, header_format)

            # Add autofilter
            if not df.empty:
                worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

    def _calculate_column_widths(self, df: pd.DataFrame) -> Dict[str, int]:
        """Calculate optimal column widths for Excel files."""
        column_widths = {}

        for col in df.columns:
            # Get max length of column content
            max_content_length = df[col].astype(str).str.len().max()
            header_length = len(str(col))

            # Use the larger of content or header length, with some padding
            optimal_width = max(max_content_length, header_length) + 2

            # Cap the width to reasonable limits
            column_widths[col] = min(max(optimal_width, 8), 50)

        return column_widths

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for all queued tasks to complete.

        Args:
            timeout: Maximum time to wait in seconds.

        Returns:
            bool: True if all tasks completed, False if timeout occurred.
        """
        try:
            if timeout is None:
                self.queue.join()
                return True
            else:
                # Manual timeout implementation since queue.join() doesn't support timeout
                start_time = time.time()
                while not self.queue.empty():
                    if time.time() - start_time > timeout:
                        return False
                    time.sleep(0.1)
                return True
        except Exception as e:
            self.logger.error(f"Error waiting for completion: {e}")
            return False

    def get_stats(self) -> Dict[str, int]:
        """Get statistics about the debugger's operations."""
        return self.stats.copy()

    def get_queue_size(self) -> int:
        """Get the current number of pending tasks."""
        return self.queue.qsize()

    def stop(self):
        """Stop the worker thread and clean up resources."""
        if hasattr(self, 'stop_event') and not self.stop_event.is_set():
            self.logger.info("Stopping DataframeDebugger...")

            # Signal stop and add sentinel
            self.stop_event.set()
            self.queue.put(None)

            # Wait for worker thread to finish
            if self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5.0)
                if self.worker_thread.is_alive():
                    self.logger.warning("Worker thread did not stop gracefully")

            self.logger.info(f"DataframeDebugger stopped. Stats: {self.get_stats()}")

    @contextmanager
    def batch_save(self):
        """
        Context manager for batch operations.
        Usage:
            with debugger.batch_save():
                debugger.debug_dataframe(df1, "file1.xlsx")
                debugger.debug_dataframe(df2, "file2.csv")
            # All files will be saved before exiting the context
        """
        try:
            yield self
        finally:
            self.wait_for_completion()


# Example usage and convenience functions
def quick_save_dataframe(
        df: pd.DataFrame,
        filename: Optional[str] = None,
        path: Optional[str] = None,
        **kwargs
) -> bool:
    """
    Quick utility function to save a DataFrame without managing the debugger instance.

    Args:
        df: DataFrame to save
        filename: Output filename
        path: Output directory
        **kwargs: Additional arguments for DataframeDebugger

    Returns:
        bool: True if successful
    """
    try:
        with DataframeDebugger(path=path, **kwargs) as debugger:
            success = debugger.debug_dataframe(df, filename)
            if success:
                debugger.wait_for_completion()
            return success
    except Exception as e:
        logging.error(f"Quick save failed: {e}")
        return False


if __name__ == "__main__":
    # Example usage
    import numpy as np

    # Create sample data
    sample_df = pd.DataFrame({
        'id': range(1, 101),
        'name': [f'Item_{i}' for i in range(1, 101)],
        'value': np.random.randn(100),
        'timestamp': pd.date_range('2024-01-01', periods=100, freq='D'),
        'category': np.random.choice(['A', 'B', 'C'], 100)
    })

    # Usage example 1: Basic usage
    with DataframeDebugger() as debugger:
        debugger.debug_dataframe(sample_df, "sample_data.xlsx")
        debugger.debug_dataframe(sample_df, "sample_data.csv")
        debugger.wait_for_completion()
        print(f"Final stats: {debugger.get_stats()}")

    # Usage example 2: Quick save
    quick_save_dataframe(sample_df, "quick_save.xlsx")

    # Usage example 3: Batch operations
    with DataframeDebugger() as debugger:
        with debugger.batch_save():
            for i in range(5):
                subset_df = sample_df.iloc[i * 20:(i + 1) * 20]
                debugger.debug_dataframe(subset_df, f"batch_{i}.xlsx")